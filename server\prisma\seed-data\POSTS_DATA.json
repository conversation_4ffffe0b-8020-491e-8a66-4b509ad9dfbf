[{"title": "Getting Started with React Hooks: A Complete Guide", "description": "Learn how to use React Hooks to manage state and side effects in functional components. This comprehensive guide covers useState, useEffect, and custom hooks.", "content": "React Hooks revolutionized how we write React components by allowing us to use state and other React features in functional components. In this comprehensive guide, we'll explore the most commonly used hooks and learn how to create our own custom hooks.\n\n## What are React Hooks?\n\nHooks are functions that let you 'hook into' React state and lifecycle features from functional components. They were introduced in React 16.8 and have become the preferred way to write React components.\n\n## useState Hook\n\nThe useState hook allows you to add state to functional components:\n\n```javascript\nimport React, { useState } from 'react';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  return (\n    <div>\n      <p>You clicked {count} times</p>\n      <button onClick={() => setCount(count + 1)}>\n        Click me\n      </button>\n    </div>\n  );\n}\n```\n\n## useEffect Hook\n\nThe useEffect hook lets you perform side effects in functional components. It serves the same purpose as componentDidMount, componentDidUpdate, and componentWillUnmount combined.\n\n```javascript\nimport React, { useState, useEffect } from 'react';\n\nfunction UserProfile({ userId }) {\n  const [user, setUser] = useState(null);\n\n  useEffect(() => {\n    fetchUser(userId).then(setUser);\n  }, [userId]);\n\n  return user ? <div>{user.name}</div> : <div>Loading...</div>;\n}\n```\n\n## Custom Hooks\n\nCustom hooks allow you to extract component logic into reusable functions:\n\n```javascript\nfunction useCounter(initialValue = 0) {\n  const [count, setCount] = useState(initialValue);\n\n  const increment = () => setCount(count + 1);\n  const decrement = () => setCount(count - 1);\n  const reset = () => setCount(initialValue);\n\n  return { count, increment, decrement, reset };\n}\n```\n\nHooks have made React development more intuitive and have opened up new patterns for sharing logic between components. Start incorporating them into your React applications today!", "published": true, "publishedAt": "2024-01-15T10:30:00Z", "imageURL": "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop"}, {"title": "Building Scalable APIs with Node.js and Express", "description": "Learn best practices for building robust and scalable REST APIs using Node.js and Express. Covers middleware, error handling, authentication, and database integration.", "content": "Building scalable APIs is crucial for modern web applications. In this guide, we'll explore how to create robust REST APIs using Node.js and Express, following industry best practices.\n\n## Setting Up Your Express Server\n\nFirst, let's set up a basic Express server with proper middleware:\n\n```javascript\nconst express = require('express');\nconst cors = require('cors');\nconst helmet = require('helmet');\nconst rateLimit = require('express-rate-limit');\n\nconst app = express();\n\n// Security middleware\napp.use(helmet());\napp.use(cors());\n\n// Rate limiting\nconst limiter = rateLimit({\n  windowMs: 15 * 60 * 1000, // 15 minutes\n  max: 100 // limit each IP to 100 requests per windowMs\n});\napp.use(limiter);\n\n// Body parsing middleware\napp.use(express.json({ limit: '10mb' }));\napp.use(express.urlencoded({ extended: true }));\n```\n\n## Structuring Your Routes\n\nOrganize your routes using Express Router:\n\n```javascript\n// routes/users.js\nconst express = require('express');\nconst router = express.Router();\nconst { authenticateToken } = require('../middleware/auth');\n\nrouter.get('/', authenticateToken, async (req, res) => {\n  try {\n    const users = await User.find().select('-password');\n    res.json(users);\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n});\n\nrouter.post('/', async (req, res) => {\n  try {\n    const user = new User(req.body);\n    await user.save();\n    res.status(201).json(user);\n  } catch (error) {\n    res.status(400).json({ error: error.message });\n  }\n});\n\nmodule.exports = router;\n```\n\n## Error Handling Middleware\n\nImplement centralized error handling:\n\n```javascript\n// middleware/errorHandler.js\nconst errorHandler = (err, req, res, next) => {\n  console.error(err.stack);\n\n  if (err.name === 'ValidationError') {\n    return res.status(400).json({\n      error: 'Validation Error',\n      details: err.message\n    });\n  }\n\n  if (err.name === 'CastError') {\n    return res.status(400).json({\n      error: 'Invalid ID format'\n    });\n  }\n\n  res.status(500).json({\n    error: 'Internal Server Error'\n  });\n};\n\nmodule.exports = errorHandler;\n```\n\n## Database Integration\n\nUse connection pooling and proper error handling:\n\n```javascript\nconst mongoose = require('mongoose');\n\nconst connectDB = async () => {\n  try {\n    const conn = await mongoose.connect(process.env.MONGODB_URI, {\n      useNewUrlParser: true,\n      useUnifiedTopology: true,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n    });\n    console.log(`MongoDB Connected: ${conn.connection.host}`);\n  } catch (error) {\n    console.error(error);\n    process.exit(1);\n  }\n};\n```\n\nBy following these patterns, you'll build APIs that are maintainable, secure, and ready to scale with your application's growth.", "published": true, "publishedAt": "2024-01-20T14:15:00Z", "imageURL": "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=400&fit=crop"}, {"title": "Mastering CSS Grid: Modern Layout Techniques", "description": "Discover the power of CSS Grid for creating complex, responsive layouts. Learn grid fundamentals, advanced techniques, and real-world examples.", "content": "CSS Grid has revolutionized how we approach web layouts. Unlike Flexbox, which is one-dimensional, Grid is a two-dimensional layout system that gives you complete control over both rows and columns.\n\n## Grid Fundamentals\n\nLet's start with a basic grid container:\n\n```css\n.grid-container {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  grid-template-rows: auto;\n  gap: 20px;\n  padding: 20px;\n}\n\n.grid-item {\n  background: #f0f0f0;\n  padding: 20px;\n  border-radius: 8px;\n}\n```\n\n## Advanced Grid Techniques\n\n### Named Grid Lines\n\n```css\n.layout {\n  display: grid;\n  grid-template-columns: [sidebar-start] 250px [sidebar-end main-start] 1fr [main-end];\n  grid-template-rows: [header-start] 60px [header-end content-start] 1fr [content-end footer-start] 40px [footer-end];\n}\n\n.header {\n  grid-column: sidebar-start / main-end;\n  grid-row: header-start / header-end;\n}\n```\n\n### Grid Areas\n\n```css\n.app-layout {\n  display: grid;\n  grid-template-areas:\n    'header header header'\n    'sidebar main aside'\n    'footer footer footer';\n  grid-template-columns: 200px 1fr 150px;\n  grid-template-rows: 60px 1fr 40px;\n  min-height: 100vh;\n}\n\n.header { grid-area: header; }\n.sidebar { grid-area: sidebar; }\n.main { grid-area: main; }\n.aside { grid-area: aside; }\n.footer { grid-area: footer; }\n```\n\n## Responsive Grid Layouts\n\n```css\n.responsive-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n@media (max-width: 768px) {\n  .app-layout {\n    grid-template-areas:\n      'header'\n      'main'\n      'sidebar'\n      'aside'\n      'footer';\n    grid-template-columns: 1fr;\n  }\n}\n```\n\n## Grid vs Flexbox\n\nUse Grid when:\n- You need two-dimensional layouts\n- You want to control both rows and columns\n- You're creating complex page layouts\n\nUse Flexbox when:\n- You need one-dimensional layouts\n- You're aligning items in a container\n- You're working with navigation bars or card layouts\n\nCSS Grid opens up endless possibilities for creative and functional web layouts. Start experimenting with these techniques in your next project!", "published": true, "publishedAt": "2024-02-01T09:45:00Z", "imageURL": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop"}, {"title": "Docker for Developers: Containerizing Your Applications", "description": "Learn how to use Docker to containerize your applications, create reproducible development environments, and streamline your deployment process.", "content": "Docker has become an essential tool for modern software development. It allows you to package your applications with all their dependencies into lightweight, portable containers.\n\n## What is Docker?\n\nDocker is a containerization platform that enables you to package applications and their dependencies into containers. These containers can run consistently across different environments.\n\n## Getting Started with Docker\n\n### Creating Your First Dockerfile\n\n```dockerfile\n# Use official Node.js runtime as base image\nFROM node:18-alpine\n\n# Set working directory in container\nWORKDIR /app\n\n# Copy package files\nCOPY package*.json ./\n\n# Install dependencies\nRUN npm ci --only=production\n\n# Copy application code\nCOPY . .\n\n# Expose port\nEXPOSE 3000\n\n# Define command to run application\nCMD [\"npm\", \"start\"]\n```\n\n### Building and Running Containers\n\n```bash\n# Build the image\ndocker build -t my-app .\n\n# Run the container\ndocker run -p 3000:3000 my-app\n\n# Run in detached mode\ndocker run -d -p 3000:3000 --name my-app-container my-app\n```\n\n## Docker Compose for Multi-Service Applications\n\n```yaml\n# docker-compose.yml\nversion: '3.8'\n\nservices:\n  app:\n    build: .\n    ports:\n      - '3000:3000'\n    environment:\n      - NODE_ENV=development\n      - DATABASE_URL=**********************************/myapp\n    depends_on:\n      - db\n      - redis\n    volumes:\n      - .:/app\n      - /app/node_modules\n\n  db:\n    image: postgres:15\n    environment:\n      - POSTGRES_DB=myapp\n      - POSTGRES_USER=user\n      - POSTGRES_PASSWORD=password\n    volumes:\n      - postgres_data:/var/lib/postgresql/data\n    ports:\n      - '5432:5432'\n\n  redis:\n    image: redis:7-alpine\n    ports:\n      - '6379:6379'\n\nvolumes:\n  postgres_data:\n```\n\n## Best Practices\n\n### Multi-stage Builds\n\n```dockerfile\n# Build stage\nFROM node:18-alpine AS builder\nWORKDIR /app\nCOPY package*.json ./\nRUN npm ci\nCOPY . .\nRUN npm run build\n\n# Production stage\nFROM node:18-alpine AS production\nWORKDIR /app\nCOPY package*.json ./\nRUN npm ci --only=production && npm cache clean --force\nCOPY --from=builder /app/dist ./dist\nEXPOSE 3000\nCMD [\"npm\", \"start\"]\n```\n\n### Security Considerations\n\n```dockerfile\n# Create non-root user\nRUN addgroup -g 1001 -S nodejs\nRUN adduser -S nextjs -u 1001\n\n# Change ownership of app directory\nCHOWN nextjs:nodejs /app\nUSER nextjs\n```\n\nDocker simplifies development workflows and ensures consistency across environments. Start containerizing your applications today to improve your development and deployment processes!", "published": false, "publishedAt": null, "imageURL": "https://images.unsplash.com/photo-1605745341112-85968b19335a?w=800&h=400&fit=crop"}, {"title": "TypeScript Best Practices for Large-Scale Applications", "description": "Explore advanced TypeScript patterns and best practices for building maintainable, type-safe applications at scale.", "content": "TypeScript has become the go-to choice for large-scale JavaScript applications. Its static type system helps catch errors early and improves code maintainability.\n\n## Advanced Type Patterns\n\n### Utility Types\n\n```typescript\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n  password: string;\n  createdAt: Date;\n}\n\n// Pick specific properties\ntype UserProfile = Pick<User, 'id' | 'name' | 'email'>;\n\n// Omit sensitive properties\ntype SafeUser = Omit<User, 'password'>;\n\n// Make all properties optional\ntype PartialUser = Partial<User>;\n\n// Make all properties required\ntype RequiredUser = Required<Partial<User>>;\n```\n\n### Generic Constraints\n\n```typescript\ninterface Identifiable {\n  id: string;\n}\n\nclass Repository<T extends Identifiable> {\n  private items: T[] = [];\n\n  add(item: T): void {\n    this.items.push(item);\n  }\n\n  findById(id: string): T | undefined {\n    return this.items.find(item => item.id === id);\n  }\n\n  update(id: string, updates: Partial<T>): T | undefined {\n    const index = this.items.findIndex(item => item.id === id);\n    if (index !== -1) {\n      this.items[index] = { ...this.items[index], ...updates };\n      return this.items[index];\n    }\n    return undefined;\n  }\n}\n```\n\n### Conditional Types\n\n```typescript\ntype ApiResponse<T> = T extends string\n  ? { message: T }\n  : T extends number\n  ? { count: T }\n  : { data: T };\n\ntype StringResponse = ApiResponse<string>; // { message: string }\ntype NumberResponse = ApiResponse<number>; // { count: number }\ntype ObjectResponse = ApiResponse<User>; // { data: User }\n```\n\n## Organizing Large Codebases\n\n### Barrel Exports\n\n```typescript\n// types/index.ts\nexport type { User, UserProfile, SafeUser } from './user';\nexport type { Post, PostSummary } from './post';\nexport type { ApiResponse, ErrorResponse } from './api';\n\n// services/index.ts\nexport { UserService } from './user.service';\nexport { PostService } from './post.service';\nexport { AuthService } from './auth.service';\n```\n\n### Strict Configuration\n\n```json\n// tsconfig.json\n{\n  \"compilerOptions\": {\n    \"strict\": true,\n    \"noImplicitAny\": true,\n    \"noImplicitReturns\": true,\n    \"noImplicitThis\": true,\n    \"noUnusedLocals\": true,\n    \"noUnusedParameters\": true,\n    \"exactOptionalPropertyTypes\": true,\n    \"noUncheckedIndexedAccess\": true\n  }\n}\n```\n\n## Error Handling Patterns\n\n### Result Type Pattern\n\n```typescript\ntype Result<T, E = Error> = \n  | { success: true; data: T }\n  | { success: false; error: E };\n\nasync function fetchUser(id: string): Promise<Result<User>> {\n  try {\n    const user = await userRepository.findById(id);\n    if (!user) {\n      return { success: false, error: new Error('User not found') };\n    }\n    return { success: true, data: user };\n  } catch (error) {\n    return { success: false, error: error as Error };\n  }\n}\n\n// Usage\nconst result = await fetchUser('123');\nif (result.success) {\n  console.log(result.data.name); // TypeScript knows this is User\n} else {\n  console.error(result.error.message);\n}\n```\n\n## Testing with TypeScript\n\n```typescript\n// Mock with proper typing\nconst mockUserService: jest.Mocked<UserService> = {\n  findById: jest.fn(),\n  create: jest.fn(),\n  update: jest.fn(),\n  delete: jest.fn(),\n};\n\n// Type-safe test data\nconst createMockUser = (overrides: Partial<User> = {}): User => ({\n  id: '1',\n  name: 'John Doe',\n  email: '<EMAIL>',\n  password: 'hashedpassword',\n  createdAt: new Date(),\n  ...overrides,\n});\n```\n\nBy following these patterns and practices, you'll build TypeScript applications that are more maintainable, less error-prone, and easier to refactor as they grow.", "published": true, "publishedAt": "2024-02-10T16:20:00Z", "imageURL": "https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop"}, {"title": "Introduction to GraphQL: A Better Way to Build APIs", "description": "Discover GraphQL and learn how it solves common REST API problems. Explore queries, mutations, subscriptions, and schema design.", "content": "GraphQL is a query language and runtime for APIs that provides a more efficient, powerful, and flexible alternative to REST. Developed by Facebook, it allows clients to request exactly the data they need.\n\n## What Makes GraphQL Different?\n\nUnlike REST APIs that expose multiple endpoints, GraphQL exposes a single endpoint and lets clients specify exactly what data they want.\n\n### REST vs GraphQL\n\n```javascript\n// REST - Multiple requests needed\nGET /users/1\nGET /users/1/posts\nGET /posts/1/comments\n\n// GraphQL - Single request\nquery {\n  user(id: 1) {\n    name\n    email\n    posts {\n      title\n      content\n      comments {\n        content\n        author {\n          name\n        }\n      }\n    }\n  }\n}\n```\n\n## Setting Up a GraphQL Server\n\n```javascript\nconst { ApolloServer, gql } = require('apollo-server-express');\nconst express = require('express');\n\n// Define schema\nconst typeDefs = gql`\n  type User {\n    id: ID!\n    name: String!\n    email: String!\n    posts: [Post!]!\n  }\n\n  type Post {\n    id: ID!\n    title: String!\n    content: String!\n    author: User!\n    comments: [Comment!]!\n  }\n\n  type Comment {\n    id: ID!\n    content: String!\n    author: User!\n    post: Post!\n  }\n\n  type Query {\n    users: [User!]!\n    user(id: ID!): User\n    posts: [Post!]!\n    post(id: ID!): Post\n  }\n\n  type Mutation {\n    createUser(name: String!, email: String!): User!\n    createPost(title: String!, content: String!, authorId: ID!): Post!\n    updatePost(id: ID!, title: String, content: String): Post\n    deletePost(id: ID!): Boolean!\n  }\n`;\n\n// Define resolvers\nconst resolvers = {\n  Query: {\n    users: () => User.findAll(),\n    user: (_, { id }) => User.findByPk(id),\n    posts: () => Post.findAll(),\n    post: (_, { id }) => Post.findByPk(id),\n  },\n  Mutation: {\n    createUser: (_, { name, email }) => User.create({ name, email }),\n    createPost: (_, { title, content, authorId }) => \n      Post.create({ title, content, authorId }),\n    updatePost: async (_, { id, ...updates }) => {\n      await Post.update(updates, { where: { id } });\n      return Post.findByPk(id);\n    },\n    deletePost: async (_, { id }) => {\n      const deleted = await Post.destroy({ where: { id } });\n      return deleted > 0;\n    },\n  },\n  User: {\n    posts: (user) => user.getPosts(),\n  },\n  Post: {\n    author: (post) => post.getUser(),\n    comments: (post) => post.getComments(),\n  },\n};\n```\n\n## Advanced Features\n\n### Subscriptions for Real-time Updates\n\n```javascript\nconst { PubSub } = require('graphql-subscriptions');\nconst pubsub = new PubSub();\n\nconst typeDefs = gql`\n  type Subscription {\n    postAdded: Post!\n    commentAdded(postId: ID!): Comment!\n  }\n`;\n\nconst resolvers = {\n  Subscription: {\n    postAdded: {\n      subscribe: () => pubsub.asyncIterator(['POST_ADDED']),\n    },\n    commentAdded: {\n      subscribe: (_, { postId }) => \n        pubsub.asyncIterator([`COMMENT_ADDED_${postId}`]),\n    },\n  },\n  Mutation: {\n    createPost: async (_, args) => {\n      const post = await Post.create(args);\n      pubsub.publish('POST_ADDED', { postAdded: post });\n      return post;\n    },\n  },\n};\n```\n\n### Input Validation and Error Handling\n\n```javascript\nconst { UserInputError, AuthenticationError } = require('apollo-server-express');\n\nconst resolvers = {\n  Mutation: {\n    createUser: async (_, { name, email }) => {\n      if (!email.includes('@')) {\n        throw new UserInputError('Invalid email format');\n      }\n      \n      try {\n        return await User.create({ name, email });\n      } catch (error) {\n        if (error.name === 'SequelizeUniqueConstraintError') {\n          throw new UserInputError('Email already exists');\n        }\n        throw error;\n      }\n    },\n  },\n};\n```\n\nGraphQL provides a powerful, flexible approach to API development that can significantly improve both developer experience and application performance.", "published": true, "publishedAt": "2024-02-15T11:30:00Z", "imageURL": "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=400&fit=crop"}, {"title": "Building Responsive Web Apps with Tailwind CSS", "description": "Learn how to create beautiful, responsive user interfaces using Tailwind CSS utility classes and responsive design principles.", "content": "Tailwind CSS is a utility-first CSS framework that provides low-level utility classes to build custom designs without leaving your HTML. It's become incredibly popular for its flexibility and developer experience.\n\n## Why Tailwind CSS?\n\n- **Utility-first approach**: Build designs using small, composable utilities\n- **Responsive by default**: Built-in responsive design system\n- **Customizable**: Easily customize colors, spacing, typography, and more\n- **Performance**: Only includes the CSS you actually use\n\n## Getting Started\n\n### Installation\n\n```bash\nnpm install -D tailwindcss postcss autoprefixer\nnpx tailwindcss init -p\n```\n\n### Configuration\n\n```javascript\n// tailwind.config.js\nmodule.exports = {\n  content: ['./src/**/*.{html,js,jsx,ts,tsx}'],\n  theme: {\n    extend: {\n      colors: {\n        primary: {\n          50: '#eff6ff',\n          500: '#3b82f6',\n          900: '#1e3a8a',\n        },\n      },\n      fontFamily: {\n        sans: ['Inter', 'sans-serif'],\n      },\n    },\n  },\n  plugins: [],\n}\n```\n\n## Responsive Design\n\n### Mobile-First Approach\n\n```html\n<!-- Stack on mobile, side-by-side on tablet and up -->\n<div class=\"flex flex-col md:flex-row gap-4\">\n  <div class=\"w-full md:w-1/3\">\n    <img src=\"image.jpg\" class=\"w-full h-48 object-cover rounded-lg\" />\n  </div>\n  <div class=\"w-full md:w-2/3\">\n    <h2 class=\"text-xl md:text-2xl font-bold mb-2\">Article Title</h2>\n    <p class=\"text-gray-600 text-sm md:text-base\">Article description...</p>\n  </div>\n</div>\n```\n\n### Responsive Grid Layouts\n\n```html\n<!-- Responsive card grid -->\n<div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n  <div class=\"bg-white rounded-lg shadow-md p-6\">\n    <h3 class=\"text-lg font-semibold mb-2\">Card Title</h3>\n    <p class=\"text-gray-600\">Card content...</p>\n  </div>\n  <!-- More cards... -->\n</div>\n```\n\n## Component Patterns\n\n### Button Component\n\n```html\n<!-- Base button styles -->\n<button class=\"px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2\">\n  Button\n</button>\n\n<!-- Primary button -->\n<button class=\"px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\">\n  Primary Button\n</button>\n\n<!-- Secondary button -->\n<button class=\"px-4 py-2 bg-gray-200 text-gray-900 rounded-md font-medium hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors\">\n  Secondary Button\n</button>\n```\n\n### Card Component\n\n```html\n<div class=\"bg-white rounded-lg shadow-md overflow-hidden\">\n  <img src=\"image.jpg\" class=\"w-full h-48 object-cover\" />\n  <div class=\"p-6\">\n    <div class=\"flex items-center justify-between mb-2\">\n      <span class=\"text-sm text-blue-600 font-medium\">Category</span>\n      <span class=\"text-sm text-gray-500\">5 min read</span>\n    </div>\n    <h3 class=\"text-xl font-bold mb-2\">Article Title</h3>\n    <p class=\"text-gray-600 mb-4\">Article excerpt...</p>\n    <div class=\"flex items-center justify-between\">\n      <div class=\"flex items-center space-x-2\">\n        <img src=\"avatar.jpg\" class=\"w-8 h-8 rounded-full\" />\n        <span class=\"text-sm text-gray-700\">Author Name</span>\n      </div>\n      <span class=\"text-sm text-gray-500\">Jan 15, 2024</span>\n    </div>\n  </div>\n</div>\n```\n\n## Advanced Techniques\n\n### Custom Utilities with @apply\n\n```css\n@layer components {\n  .btn-primary {\n    @apply px-4 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;\n  }\n  \n  .card {\n    @apply bg-white rounded-lg shadow-md overflow-hidden;\n  }\n}\n```\n\n### Dark Mode Support\n\n```html\n<div class=\"bg-white dark:bg-gray-800 text-gray-900 dark:text-white\">\n  <h1 class=\"text-2xl font-bold\">Hello World</h1>\n  <p class=\"text-gray-600 dark:text-gray-300\">This works in both light and dark mode.</p>\n</div>\n```\n\nTailwind CSS enables rapid UI development while maintaining design consistency. Its utility-first approach might feel different at first, but it leads to more maintainable and flexible stylesheets.", "published": false, "publishedAt": null, "imageURL": "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=800&h=400&fit=crop"}, {"title": "Securing Your Web Applications: Essential Security Practices", "description": "Learn essential web security practices to protect your applications from common vulnerabilities and attacks.", "content": "Web security is crucial for protecting your applications and users' data. This guide covers essential security practices every developer should implement.\n\n## Common Security Vulnerabilities\n\n### 1. Cross-Site Scripting (XSS)\n\nXSS attacks inject malicious scripts into web pages viewed by other users.\n\n**Prevention:**\n\n```javascript\n// Bad - Direct HTML insertion\ndocument.getElementById('content').innerHTML = userInput;\n\n// Good - Use textContent or sanitize HTML\ndocument.getElementById('content').textContent = userInput;\n\n// Or use a sanitization library\nconst DOMPurify = require('dompurify');\ndocument.getElementById('content').innerHTML = DOMPurify.sanitize(userInput);\n```\n\n### 2. SQL Injection\n\nSQL injection occurs when user input is directly concatenated into SQL queries.\n\n**Prevention:**\n\n```javascript\n// Bad - String concatenation\nconst query = `SELECT * FROM users WHERE email = '${email}'`;\n\n// Good - Parameterized queries\nconst query = 'SELECT * FROM users WHERE email = ?';\nconst result = await db.query(query, [email]);\n\n// With Prisma (automatically prevents SQL injection)\nconst user = await prisma.user.findUnique({\n  where: { email: email }\n});\n```\n\n### 3. Cross-Site Request Forgery (CSRF)\n\nCSRF attacks trick users into performing unwanted actions on applications where they're authenticated.\n\n**Prevention:**\n\n```javascript\nconst csrf = require('csurf');\nconst csrfProtection = csrf({ cookie: true });\n\napp.use(csrfProtection);\n\napp.get('/form', (req, res) => {\n  res.render('form', { csrfToken: req.csrfToken() });\n});\n```\n\n## Authentication and Authorization\n\n### Secure Password Handling\n\n```javascript\nconst bcrypt = require('bcrypt');\nconst saltRounds = 12;\n\n// Hash password before storing\nconst hashPassword = async (password) => {\n  return await bcrypt.hash(password, saltRounds);\n};\n\n// Verify password during login\nconst verifyPassword = async (password, hashedPassword) => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n```\n\n### JWT Security\n\n```javascript\nconst jwt = require('jsonwebtoken');\n\n// Generate token with expiration\nconst generateToken = (userId) => {\n  return jwt.sign(\n    { userId },\n    process.env.JWT_SECRET,\n    { \n      expiresIn: '15m',\n      issuer: 'your-app-name',\n      audience: 'your-app-users'\n    }\n  );\n};\n\n// Verify token middleware\nconst authenticateToken = (req, res, next) => {\n  const authHeader = req.headers['authorization'];\n  const token = authHeader && authHeader.split(' ')[1];\n\n  if (!token) {\n    return res.sendStatus(401);\n  }\n\n  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {\n    if (err) return res.sendStatus(403);\n    req.user = user;\n    next();\n  });\n};\n```\n\n## Security Headers\n\n```javascript\nconst helmet = require('helmet');\n\napp.use(helmet({\n  contentSecurityPolicy: {\n    directives: {\n      defaultSrc: [\"'self'\"],\n      styleSrc: [\"'self'\", \"'unsafe-inline'\", 'https://fonts.googleapis.com'],\n      fontSrc: [\"'self'\", 'https://fonts.gstatic.com'],\n      imgSrc: [\"'self'\", 'data:', 'https:'],\n      scriptSrc: [\"'self'\"],\n    },\n  },\n  hsts: {\n    maxAge: 31536000,\n    includeSubDomains: true,\n    preload: true\n  }\n}));\n```\n\n## Input Validation\n\n```javascript\nconst Joi = require('joi');\n\nconst userSchema = Joi.object({\n  email: Joi.string().email().required(),\n  password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\\$%\\^&\\*])')).required(),\n  name: Joi.string().min(2).max(50).required()\n});\n\nconst validateUser = (req, res, next) => {\n  const { error } = userSchema.validate(req.body);\n  if (error) {\n    return res.status(400).json({ error: error.details[0].message });\n  }\n  next();\n};\n```\n\n## Rate Limiting\n\n```javascript\nconst rateLimit = require('express-rate-limit');\n\nconst loginLimiter = rateLimit({\n  windowMs: 15 * 60 * 1000, // 15 minutes\n  max: 5, // limit each IP to 5 requests per windowMs\n  message: 'Too many login attempts, please try again later.',\n  standardHeaders: true,\n  legacyHeaders: false,\n});\n\napp.post('/login', loginLimiter, (req, res) => {\n  // Login logic\n});\n```\n\n## Environment Security\n\n```javascript\n// Use environment variables for sensitive data\nconst config = {\n  jwtSecret: process.env.JWT_SECRET,\n  dbUrl: process.env.DATABASE_URL,\n  apiKey: process.env.API_KEY,\n};\n\n// Validate required environment variables\nconst requiredEnvVars = ['JWT_SECRET', 'DATABASE_URL'];\nrequiredEnvVars.forEach(envVar => {\n  if (!process.env[envVar]) {\n    throw new Error(`Missing required environment variable: ${envVar}`);\n  }\n});\n```\n\nSecurity is an ongoing process, not a one-time setup. Regularly update dependencies, conduct security audits, and stay informed about new vulnerabilities and best practices.", "published": true, "publishedAt": "2024-02-20T13:45:00Z", "imageURL": "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=400&fit=crop"}]