<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1759815007170" clover="3.2.0">
  <project timestamp="1759815007170" name="All files">
    <metrics statements="167" coveredstatements="159" conditionals="51" coveredconditionals="42" methods="28" coveredmethods="28" elements="246" coveredelements="229" complexity="0" loc="167" ncloc="167" packages="6" files="8" classes="8"/>
    <package name="api.Category">
      <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="5"/>
      <file name="category.services.ts" path="E:\The-Salty-Devs\server\src\api\Category\category.services.ts">
        <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="5"/>
        <line num="1" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="71" count="4" type="stmt"/>
        <line num="73" count="4" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
      </file>
    </package>
    <package name="api.Comments">
      <metrics statements="22" coveredstatements="22" conditionals="7" coveredconditionals="6" methods="6" coveredmethods="6"/>
      <file name="comments.services.ts" path="E:\The-Salty-Devs\server\src\api\Comments\comments.services.ts">
        <metrics statements="22" coveredstatements="22" conditionals="7" coveredconditionals="6" methods="6" coveredmethods="6"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="82" count="2" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="102" count="7" type="stmt"/>
        <line num="104" count="7" type="cond" truecount="5" falsecount="0"/>
        <line num="105" count="3" type="stmt"/>
        <line num="110" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="112" count="4" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="165" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
      </file>
    </package>
    <package name="api.Post">
      <metrics statements="58" coveredstatements="55" conditionals="28" coveredconditionals="23" methods="8" coveredmethods="8"/>
      <file name="post.services.ts" path="E:\The-Salty-Devs\server\src\api\Post\post.services.ts">
        <metrics statements="58" coveredstatements="55" conditionals="28" coveredconditionals="23" methods="8" coveredmethods="8"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="9" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="11" count="1" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="20" count="2" type="stmt"/>
        <line num="23" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="1" type="stmt"/>
        <line num="27" count="2" type="stmt"/>
        <line num="32" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="33" count="2" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="153" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="161" count="3" type="stmt"/>
        <line num="170" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="171" count="2" type="stmt"/>
        <line num="178" count="2" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="193" count="3" type="stmt"/>
        <line num="230" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="231" count="0" type="stmt"/>
        <line num="234" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="235" count="3" type="stmt"/>
        <line num="238" count="3" type="stmt"/>
        <line num="241" count="1" type="stmt"/>
        <line num="251" count="2" type="stmt"/>
        <line num="253" count="2" type="cond" truecount="5" falsecount="0"/>
        <line num="254" count="1" type="stmt"/>
        <line num="260" count="1" type="stmt"/>
        <line num="266" count="1" type="stmt"/>
        <line num="268" count="1" type="stmt"/>
        <line num="299" count="1" type="stmt"/>
        <line num="304" count="3" type="stmt"/>
        <line num="306" count="3" type="stmt"/>
        <line num="311" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="312" count="1" type="stmt"/>
        <line num="315" count="2" type="stmt"/>
        <line num="317" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="318" count="1" type="stmt"/>
        <line num="324" count="1" type="stmt"/>
        <line num="326" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="327" count="1" type="stmt"/>
        <line num="328" count="1" type="stmt"/>
        <line num="331" count="1" type="stmt"/>
        <line num="364" count="1" type="stmt"/>
        <line num="366" count="1" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
      </file>
    </package>
    <package name="api.User">
      <metrics statements="43" coveredstatements="43" conditionals="10" coveredconditionals="10" methods="7" coveredmethods="7"/>
      <file name="user.services.ts" path="E:\The-Salty-Devs\server\src\api\User\user.services.ts">
        <metrics statements="43" coveredstatements="43" conditionals="10" coveredconditionals="10" methods="7" coveredmethods="7"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="3" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="2" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="2" type="stmt"/>
        <line num="67" count="2" type="stmt"/>
        <line num="73" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="74" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="93" count="3" type="stmt"/>
        <line num="94" count="3" type="stmt"/>
        <line num="100" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="101" count="1" type="stmt"/>
        <line num="104" count="2" type="stmt"/>
        <line num="106" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="107" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="119" count="1" type="stmt"/>
        <line num="122" count="1" type="stmt"/>
        <line num="127" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="144" count="2" type="stmt"/>
        <line num="146" count="2" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="163" count="3" type="stmt"/>
        <line num="167" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="168" count="1" type="stmt"/>
        <line num="171" count="2" type="stmt"/>
        <line num="175" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="176" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="1" type="stmt"/>
      </file>
    </package>
    <package name="constants">
      <metrics statements="11" coveredstatements="6" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="config.ts" path="E:\The-Salty-Devs\server\src\constants\config.ts">
        <metrics statements="11" coveredstatements="6" conditionals="2" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="20" coveredstatements="20" conditionals="4" coveredconditionals="3" methods="2" coveredmethods="2"/>
      <file name="appError.ts" path="E:\The-Salty-Devs\server\src\utils\appError.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="3" count="3" type="stmt"/>
        <line num="6" count="11" type="stmt"/>
        <line num="7" count="11" type="stmt"/>
        <line num="8" count="11" type="stmt"/>
        <line num="9" count="11" type="stmt"/>
        <line num="10" count="11" type="stmt"/>
      </file>
      <file name="db.config.ts" path="E:\The-Salty-Devs\server\src\utils\db.config.ts">
        <metrics statements="4" coveredstatements="4" conditionals="2" coveredconditionals="1" methods="0" coveredmethods="0"/>
        <line num="1" count="4" type="stmt"/>
        <line num="9" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="10" count="4" type="stmt"/>
        <line num="18" count="4" type="stmt"/>
      </file>
      <file name="httpStatusCodes.ts" path="E:\The-Salty-Devs\server\src\utils\httpStatusCodes.ts">
        <metrics statements="10" coveredstatements="10" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="1" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="2" count="3" type="stmt"/>
        <line num="3" count="3" type="stmt"/>
        <line num="4" count="3" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
        <line num="6" count="3" type="stmt"/>
        <line num="7" count="3" type="stmt"/>
        <line num="8" count="3" type="stmt"/>
        <line num="9" count="3" type="stmt"/>
        <line num="10" count="3" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
