import { PrismaClient, Role } from '@prisma/client';
import bcrypt from 'bcrypt';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import * as slugifyModule from 'slugify';

// Assign the callable function (usually found on .default) to a clean variable name.
const slugify = (slugifyModule as any).default || slugifyModule;

const prisma = new PrismaClient();

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Function to generate unique slugs
const generateUniqueSlug = async (baseSlug: string): Promise<string> => {
  let uniqueSlug = baseSlug;
  let counter = 1;

  while (true) {
    const existingPost = await prisma.post.findFirst({
      where: { slug: uniqueSlug },
      select: { id: true },
    });

    if (!existingPost) {
      return uniqueSlug; // Found a unique slug
    }

    // If conflict, increment counter and check again
    counter++;
    uniqueSlug = `${baseSlug}-${counter}`;
  }
};

// Load JSON data files
const categoriesData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/CATEGORIES_DATA.json'), 'utf-8')
);

const tagsData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/TAGS_DATA.json'), 'utf-8')
);

const usersData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/USERS_DATA.json'), 'utf-8')
);

const postsData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/POSTS_DATA.json'), 'utf-8')
);

const commentsData = JSON.parse(
  readFileSync(join(__dirname, 'seed-data/COMMENTS_DATA.json'), 'utf-8')
);

async function main() {
  console.log('🌱 Starting database seeding...');

  // Clear existing data (optional - comment out if you want to keep existing data)
  console.log('🧹 Cleaning existing data...');
  await prisma.postViews.deleteMany();
  await prisma.comment.deleteMany();
  await prisma.post.deleteMany();
  await prisma.category.deleteMany();
  await prisma.user.deleteMany();

  // Create Categories from JSON
  console.log('📂 Creating categories...');
  const categories = await Promise.all(
    categoriesData.map((categoryData: any) =>
      prisma.category.create({ data: categoryData })
    )
  );

  // Extract tag names from JSON data (tags are stored as string arrays in posts)
  console.log('🏷️ Loading tag names...');
  const tagNames = tagsData.map((tagData: any) => tagData.name);

  // Hash password for users
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Create Users (including admin)
  console.log('👥 Creating users...');
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: Role.ADMIN,
      bio: 'Administrator of The Salty Devs blog. Passionate about web development and sharing knowledge.',
    },
  });

  // Create Users from JSON
  console.log('👤 Creating users from JSON...');
  const users = await Promise.all(
    usersData.map((userData: any) =>
      prisma.user.create({
        data: {
          ...userData,
          password: hashedPassword,
          role: Role.USER,
        },
      })
    )
  );

  // Create Posts from JSON
  console.log('📝 Creating posts from JSON...');
  const posts = [];
  for (let i = 0; i < postsData.length; i++) {
    const postData = postsData[i];
    const randomUser = users[Math.floor(Math.random() * users.length)];
    const randomCategory =
      categories[Math.floor(Math.random() * categories.length)];

    // Generate unique slug for the post
    const baseSlug = slugify(postData.title, {
      lower: true,
      strict: true,
      trim: true,
    });
    const postSlug = await generateUniqueSlug(baseSlug);

    const post = await prisma.post.create({
      data: {
        title: postData.title,
        description: postData.description || null,
        content: postData.content,
        slug: postSlug,
        published: postData.published,
        publishedAt: postData.publishedAt
          ? (() => {
              // Handle invalid date format like "3/13/2024T16:35:41Z"
              const dateStr = postData.publishedAt;

              if (dateStr.includes('T') && dateStr.includes('/')) {
                // Convert "3/13/2024T16:35:41Z" to "2024-03-13T16:35:41Z"
                const [datePart, timePart] = dateStr.split('T');
                const [month, day, year] = datePart.split('/');
                const isoDate = `${year}-${month.padStart(
                  2,
                  '0'
                )}-${day.padStart(2, '0')}T${timePart}`;
                return new Date(isoDate);
              }

              return new Date(dateStr);
            })()
          : null,
        imageURL: postData.imageURL,
        authorId: i === 0 ? admin.id : randomUser.id, // First post by admin
        categoryId: randomCategory.id,
        tags: tagNames.slice(0, Math.floor(Math.random() * 3) + 1),
      },
    });
    posts.push(post);
  }

  // Create Comments from JSON
  console.log('💬 Creating comments from JSON...');
  for (const post of posts) {
    const numComments = Math.floor(Math.random() * 5) + 1; // 1-5 comments per post
    for (let i = 0; i < numComments; i++) {
      const randomUser = users[Math.floor(Math.random() * users.length)];
      const randomComment =
        commentsData[Math.floor(Math.random() * commentsData.length)];

      await prisma.comment.create({
        data: {
          content: randomComment.content,
          authorId: randomUser.id,
          postId: post.id,
        },
      });
    }
  }

  console.log('✅ Seed data created successfully!');
  console.log(`📊 Created:`);
  console.log(`   - ${categories.length} categories`);
  console.log(`   - ${tagNames.length} tags`);
  console.log(`   - ${users.length + 1} users (including admin)`);
  console.log(`   - ${posts.length} posts`);
  console.log(`   - Multiple comments per post`);
  console.log(`   - Admin login: <EMAIL> / password123`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
