{"name": "server", "version": "1.0.0", "description": "Server for 'The Salty Devs' Blog app", "main": "src/server.ts", "type": "module", "scripts": {"build": "tsc", "start": "tsx src/server.ts", "dev": "nodemon --watch src --ext ts --exec \"tsx src/server.ts\"", "prisma:generate": "prisma generate", "test": "dotenv -e .env.test jest ", "seed": "tsx prisma/seed.ts"}, "repository": {"type": "git", "url": "git+https://github.com/anshoolp-endure/The-Salty-Devs.git"}, "author": "<PERSON><PERSON><PERSON> and Rhythm Naik", "license": "ISC", "bugs": {"url": "https://github.com/anshoolp-endure/The-Salty-Devs/issues"}, "homepage": "https://github.com/anshoolp-endure/The-Salty-Devs#readme", "dependencies": {"@prisma/client": "^6.16.3", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.3", "esbuild": "^0.25.10", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "slugify": "^1.6.6", "the-salty-devs": "file:..", "zod-validation-error": "^4.0.2"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^4.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.7.0", "@types/supertest": "^6.0.3", "cross-env": "^10.1.0", "dotenv-cli": "^10.0.0", "esbuild-register": "^3.6.0", "jest": "^30.2.0", "nodemon": "^3.1.10", "prisma": "^6.16.3", "prisma-zod-generator": "^1.27.3", "supertest": "^7.1.4", "ts-jest": "^29.4.4", "ts-node": "^10.9.2", "tsx": "^4.20.6", "typescript": "^5.9.3", "zod": "^4.1.12"}}